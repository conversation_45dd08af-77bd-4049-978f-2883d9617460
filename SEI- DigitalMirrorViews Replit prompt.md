Below is a copy-and-paste prompt you can drop straight into Replit Ghostwriter / “AI Generate” to scaffold the Digital Mirror prototype with two complementary views: 

Graph Mesh – an interactive, Celonis-style causal network that visualises how metrics and entities influence one another across layers. 

KPI Dashboard – a tile-based, investor-grade board showing headline numbers, trends, benchmarks and Joyce insights. 

The structure, wording and file map follow the same pattern as your previous Enterprise, Product-&-Service and Customer Layer prompts, so the generated code will slot straight into your existing Vite workspace. 

python 

CopyEdit 

# PROJECT: Rejoyce.AI – SEI Report ▸ Digital Mirror (Graph Mesh + KPI Views) 

# GOAL: Build a React + Vite + Tailwind prototype with TWO primary routes: 

#       1) /mirror/graph   – causal “mesh” view (force-directed graph) 

#       2) /mirror/kpi     – KPI-centric dashboard (modular tiles) 

# Each view shares a top-level strategic context bar (filters, Fog Score, date range) 

# and a collapsible JoycePanel for AI Q&A. 

 

############################################ 

## 1 ▸ TECH STACK & LIBRARIES 

############################################ 

- React 18 + Vite 

- TailwindCSS (pre-configured) 

- Zustand (or Context) for global state & filters 

- **react-force-graph 3D** (or 2D) for causal mesh 

- **d3-sankey** for optional flow overlays 

- **Recharts** for KPI charts (line, bar, radar, mini-sparks) 

- Lucide-react icons 

- react-router-dom for routing 

- Optional: `@react-spring/web` for smooth mesh animations 

 

############################################ 

## 2 ▸ FOLDER & COMPONENT MAP 

############################################ 

src/ 

 ┣ App.jsx                       ⟶ layout, global filters, router 

 ┣ data/ 

 ┃   ┗ mirrorMock.json           ⟶ causal nodes/links + KPI metrics + alerts 

 ┣ pages/ 

 ┃   ┣ MirrorGraph.jsx           ⟶ Graph Mesh route 

 ┃   ┗ MirrorKPI.jsx             ⟶ KPI Dashboard route 

 ┣ components/ 

 ┃   ┣ TopContextBar.jsx         ⟶ intent selector, layer filter, date range, Fog badge 

 ┃   ┣ ForceMesh.jsx             ⟶ wrapper around react-force-graph 

 ┃   ┣ SankeyOverlay.jsx         ⟶ toggleable flow overlay (optional) 

 ┃   ┣ KpiTile.jsx               ⟶ tile: metric, trend spark, bench bands, Joyce blurb 

 ┃   ┣ SignalsPanel.jsx          ⟶ alerts & strategic alignment scores 

 ┃   ┣ JoycePanel.jsx            ⟶ collapsible AI assistant 

 ┃   ┗ LegendTooltip.jsx         ⟶ colour / icon legend for mesh 

 ┣ store/filters.js              ⟶ Zustand store for intent, layer, date, unit 

 ┣ hooks/useJoyceContext.js      ⟶ passes current view & filters to JoycePanel 

 ┗ index.css, main.jsx … 

 

############################################ 

## 3 ▸ ROUTES & VIEW SPECS 

############################################ 

### /mirror/graph  (🕸️ Causal Mesh) 

- <TopContextBar /> 

- <ForceMesh /> 

    • Nodes: layers (Enterprise / Product / Customer / Execution), KPIs, Units   

    • Links: weighted influence (thickness), coloured by layer   

    • Hover → tooltip (metric, current value, trend, Fog score)   

    • Click node → JoycePanel pre-filled with “Explain impact of [node]”   

- Right Dock: <SignalsPanel /> (risk flags, alignment %)   

- Toggle buttons: 2D/3D, show Sankey flows, freeze physics   

 

### /mirror/kpi  (📊 KPI Dashboard) 

- <TopContextBar /> 

- Grid of <KpiTile /> components (drag-reorderable) 

    • Headline value, YoY / QoQ sparkline, Bench band (P25-P75), Fog indicator   

    • 1-line Joyce insight (why up/down) :contentReference[oaicite:7]{index=7}   

- <SignalsPanel /> bottom or side 

- KPI tiles sourced from layered mock (Enterprise, Product, Customer, Execution)   

  matching the “primary view” spec :contentReference[oaicite:8]{index=8}   

 

############################################ 

## 4 ▸ MOCK DATA SCHEMA (mirrorMock.json) 

############################################ 

{ 

  "strategicIntents": ["Growth","Cost","Innovation"], 

  "layers": ["Enterprise","Product","Customer","Execution"], 

  "nodes": [ 

    { "id":"Revenue Growth","layer":"Enterprise","value":0.12 }, 

    { "id":"NPS","layer":"Customer","value":65 }, 

    { "id":"Defect Rate","layer":"Execution","value":0.04 }, 

    ... 

  ], 

  "links": [ 

    { "source":"Defect Rate", "target":"NPS", "weight":0.7 }, 

    { "source":"NPS", "target":"Repeat Sales", "weight":0.6 }, 

    { "source":"Repeat Sales", "target":"Revenue Growth", "weight":0.8 } 

  ], 

  "kpis": { 

    "Revenue Growth": { "actual":12, "budget":15, "benchP25":8, "benchP75":18, "trend":[10,11,12] }, 

    "EBITDA Margin": { ... } 

  }, 

  "alerts": [ 

    { "msg":"Churn up 2 pts in Segment B", "severity":"high" }, 

    { "msg":"Defect Rate above industry P75", "severity":"med" } 

  ] 

} 

 

############################################ 

## 5 ▸ UX / UI NOTES 

############################################ 

- **Graph colours** map to layers; KPI Tile colours map to performance bands   

- TopContextBar fog badge shows data confidence (green > 0.9, amber 0.7-0.9, red < 0.7) :contentReference[oaicite:9]{index=9}   

- Layer filter lets users hide/show nodes & tiles for faster focus   

- JoycePanel auto-receives `{view, intent, selectedNode, filters}` for context   

- Animations: spring in KPI deltas; force-directed cool-down after 3 s   

 

############################################ 

## 6 ▸ DELIVERABLES 

############################################ 

1. Vite project runnable with `npm install && npm run dev`   

2. Routes:   

     • `/mirror/graph` → causal mesh view   

     • `/mirror/kpi`   → KPI dashboard view   

3. Mock data pre-wired; easy to replace with live sources   

4. Responsive layout (desktop first, mobile scroll)   

5. JoycePanel placeholder ready for OpenAI integration   

 

############################################ 

# END PROMPT – Generate full codebase now 

############################################ 

Why these design choices? 

The graph-mesh encodes the causal “what-causes-what” relationships championed in your thesis and Balanced-Scorecard lineage . 

The KPI tiles + signals panel replicate the “cockpit / strategic control room” concept you sketched , giving investment-grade at-a-glance insight. 

Shared filters, Fog Score and Joyce AI ensure continuity between views and traceability across layers, fulfilling the mirror’s purpose of instant drill-down and causal diagnosis . 

 