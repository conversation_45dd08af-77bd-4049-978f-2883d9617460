Below is a copy-and-paste Replit AI prompt that scaffolds a runnable web-app prototype for the Customer Layer of your SEI report. It mirrors the tech stack you used in the Enterprise and Product/Service layers and adds CLTV segmentation, Joy-Score analytics, and link-mapping back into the SEI “mesh.” 

pgsql 

CopyEdit 

# PROJECT: Rejoyce.AI – SEI Report ▸ Digital Mirror ▸ Customer Layer 

# GOAL: Generate a full-stack React + Vite + Tailwind prototype that lets users 

# explore Customer Lifetime Value (CLTV), customer segments, Joy-Score trends, 

# and the linkages between customer insights, product/service performance, and 

# enterprise-level financials. Include an AI “Joyce Agent” sidebar for Q&A. 

 

############################################ 

## 1 ▸ TECH STACK & LIBRARIES 

############################################ 

- React 18 + Vite starter 

- TailwindCSS for styling 

- Recharts (line, bar, radar, waterfall, sankey) 

- React Router DOM for navigation 

- Zustand (or Context) for global state 

- Lucide-react icons 

- d3-sankey (for mesh linkage visual) 

- Optional: simple JSON mock API or static JSON files 

 

############################################ 

## 2 ▸ FOLDER & COMPONENT MAP 

############################################ 

src/ 

 ┣ App.jsx                         ⟶ top-level layout + route switch 

 ┣ data/ 

 ┃   ┗ customerMock.json           ⟶ placeholder CLTV + sentiment + link data 

 ┣ pages/ 

 ┃   ┗ CustomerLayer.jsx           ⟶ container with six tabs 

 ┣ components/ 

 ┃   ┣ CLTVOverview.jsx            ⟶ headline cards, trend sparkline, peer quartiles 

 ┃   ┣ SegmentationMatrix.jsx      ⟶ interactive heatmap (segments × metrics) 

 ┃   ┣ DriversAnalysis.jsx         ⟶ correlation scatter / bar driver chart 

 ┃   ┣ JoyScoreTrends.jsx          ⟶ radar + line charts of Joy over stages 

 ┃   ┣ MeshLinkSankey.jsx          ⟶ Sankey linking customer segments to P&S units 

 ┃   ┣ ActionCard.jsx              ⟶ ‘Next best action’ recommendation tiles 

 ┃   ┣ CustomerCommentFeed.jsx     ⟶ scrollable verbatims with sentiment badges 

 ┃   ┗ JoycePanel.jsx              ⟶ collapsible chat assistant (placeholder UI) 

 

############################################ 

## 3 ▸ CUSTOMER LAYER – TAB SPEC 

############################################ 

🔷 **Tab 1: Overview** 

   - <CLTVOverview /> (overall CLTV, YoY trend, industry benchmark) 

   - High/low Joy-Score callouts 

   - Joyce quick-ask buttons 

 

🔶 **Tab 2: Segmentation** 

   - <SegmentationMatrix />: rows = segments (e.g., HV / MV / LV), cols = metrics 

   - Drill-through on segment → opens modal with demographics + behaviors 

   - Export segment CSV button 

 

🛠 **Tab 3: Drivers** 

   - <DriversAnalysis />: bar of driver coefficients + toggle to regression R² 

   - Joy-Score overlay to show emotional driver strength 

   - Joyce “What boosts CLTV fastest?” template prompt 

 

🔗 **Tab 4: Linkages** 

   - <MeshLinkSankey />: flows from customer segments → products/services → EBITDA 

   - Hover reveals % revenue, margin, Joy 

   - Secondary table: feature/service attributes loved by HV segments 

 

😀 **Tab 5: Joy & Sentiment** 

   - <JoyScoreTrends /> across journey stages 

   - Word cloud & filtered verbatim feed (<CustomerCommentFeed />) 

   - Toggle: Joy vs. NPS vs. CSAT 

 

📋 **Tab 6: Action Plan** 

   - Grid of <ActionCard /> ranked by CLTV upside & effort 

   - Traffic-light board: segment × journey pain-point 

   - Joyce suggestion wizard 

 

############################################ 

## 4 ▸ MOCK DATA SCHEMA (customerMock.json) 

############################################ 

{ 

  "segments": ["High-Value", "Mid-Value", "Low-Value"], 

  "metrics": { 

    "High-Value": { 

      "CLTV": 2350, 

      "JoyScore": 87, 

      "Retention": 0.92, 

      "AvgOrder": 180, 

      "AOVTrend": [170,175,178,180] 

    }, 

    "Mid-Value": { … } 

  }, 

  "drivers": [ 

    { "name":"Purchase Frequency", "coef":0.42 }, 

    { "name":"Average Order Value", "coef":0.31 }, 

    { "name":"JoyScore", "coef":0.27 } 

  ], 

  "joyStages": { 

    "High-Value": { "Awareness":80,"Consideration":85,"Purchase":90,"Service":88,"Loyalty":92 } 

  }, 

  "meshLinks": [ 

    { "segment":"High-Value", "product":"Product A", "revenue":3.1 }, 

    { "segment":"High-Value", "product":"Service X", "revenue":1.2 } 

  ], 

  "comments": { 

    "High-Value":[ 

      "Love the premium support!", 

      "Wish delivery were faster." 

    ] 

  }, 

  "actions": [ 

    { "segment":"Mid-Value", "recommendation":"Introduce tiered loyalty perks", "npv":25, "complexity":"Medium", "quarter":"Q4" } 

  ] 

} 

 

############################################ 

## 5 ▸ UX & INTERACTION NOTES 

############################################ 

- Global filters: time range, customer segment, region 

- Persist breadcrumb: “SEI ▸ Customer Layer” 

- Color keys: CLTV ↑ green, ↓ red; Joy ↑ teal 

- Tooltips include data lineage (“CRM FY25Q1”, “Benchmark: IDC SaaS CLTV study”) 

- JoycePanel auto-injects current tab & filters into prompt context 

 

############################################ 

## 6 ▸ DELIVERABLES 

############################################ 

1. Vite project; run with `npm install && npm run dev` 

2. Route `/report/customer` 

3. All six tabs functional with mock data 

4. Responsive design, Tailwind utility classes 

5. JoycePanel scaffold connected to tab context (fake responses ok) 

 

############################################ 

# END PROMPT – Generate full codebase now 

############################################ 

 