Here is your Replit-ready build prompt for prototyping the Product and Service Layer of the SEI report inside a full-stack React app. This builds on the pattern from the Enterprise Layer and introduces customer journey logic, product/service segmentation, execution lifecycle maps (SELM/SILM), and Joy Score tracking. 

Shape 

bash 

CopyEdit 

# PROJECT: Rejoyce.AI – SEI Report ▸ Digital Mirror ▸ Product and Service Layer 

# GOAL: Generate a working prototype (React + Vite + TailwindCSS) that implements 

# a full interactive Product and Service Layer with tabs for customer journey stages, 

# execution lifecycle (SELM/SILM), Joy Score tracking, and AI-powered Q&A via a Joyce Agent panel. 

 

############################################ 

## 1 ▸ TECH STACK & LIBRARIES 

############################################ 

- React 18 + Vite (base setup) 

- TailwindCSS for styling (pre-configured) 

- Recharts for charting (bar, line, radar, funnel) 

- React Router DOM for navigation 

- Zustand (or Context API) for state management 

- Lucide-react icons 

- Optional: Simple static JSON mock API or local JSON files 

 

############################################ 

## 2 ▸ FOLDER / COMPONENT STRUCTURE 

############################################ 

src/ 

 ┣ App.jsx                   ⟶ app layout + tabbed routes 

 ┣ data/ 

 ┃   ┗ productServiceMock.json ⟶ sample KPI, maturity, and customer journey data 

 ┣ pages/ 

 ┃   ┗ ProductServiceLayer.jsx ⟶ master container w/ tab views 

 ┣ components/ 

 ┃   ┣ JourneyStageTab.jsx       ⟶ reusable stage page: Awareness, Consideration, etc. 

 ┃   ┣ SelmSilMaps.jsx           ⟶ execution lifecycle flows w/ metrics 

 ┃   ┣ JoyScorePanel.jsx         ⟶ journey-based joy tracker per unit 

 ┃   ┣ UnitMatrixTable.jsx       ⟶ top-level KPI comparison table 

 ┃   ┣ KPIBlock.jsx              ⟶ block component for KPIs with benchmarks 

 ┃   ┣ ActionRecommendation.jsx  ⟶ cards ranking priority levers 

 ┃   ┣ CustomerCommentFeed.jsx   ⟶ scrollable customer verbatim view 

 ┃   ┗ JoycePanel.jsx            ⟶ collapsible AI Q&A assistant (placeholder) 

 

############################################ 

## 3 ▸ FEATURED TABS 

############################################ 

 

# 🔷 Tab 1: Overview & Summary 

- <UnitMatrixTable /> (CPA, Conv %, AOV, CSAT, CLTV, Joy Score by product unit) 

- <JoyScorePanel /> with heatmap view 

- Summary findings block (auto-highlight high/low units) 

- <JoycePanel /> enabled (context-aware) 

 

# 🔶 Tab 2: Customer Journey Deep Dive (5 sub-tabs) 

For each: Awareness | Consideration | Purchase | Service | Loyalty 

- <JourneyStageTab /> receives: 

  - KPIBlock components (e.g., CPA, CPL, Conv %, CLTV) 

  - Joy Score trend for stage 

  - SELM/SILM maturity data 

  - Customer quotes list (from <CustomerCommentFeed />) 

  - Benchmarks (hardcoded for now) 

 

# 🧪 Tab 3: SELM/SILM Diagnostic Maps 

- Visual execution maps (SVG or Flow components) 

- Traffic-light overlay for each lifecycle step (Lead Gen, Onboarding, Issue Resolution, etc.) 

- Toggle to switch view: SELM or SILM 

- Summary radar chart of execution maturity 

 

# 😀 Tab 4: Joy Score Analysis 

- <JoyScorePanel /> comparing products across stages 

- Trend charts + sentiment word cloud 

- Export-to-CSV button 

- “Ask Joyce” context menu: e.g., “Which unit has the largest joy-to-retention gap?” 

 

# 📋 Tab 5: Action Plan & Recommendations 

- Grid of <ActionRecommendation /> cards: 

  - NPV impact, complexity tag, unit affected, recommended quarter 

- Traffic-light matrix of unit vs. journey stage 

- Joyce-enabled recommendations explorer 

 

############################################ 

## 4 ▸ SAMPLE MOCK DATA (productServiceMock.json) 

############################################ 

{ 

  "units": ["Product A", "Product B", "Service X"], 

  "journeyStages": ["Awareness", "Consideration", "Purchase", "Service", "Loyalty"], 

  "metrics": { 

    "Product A": { 

      "Awareness": { "CPA": 15.50, "CTR": 0.03, "JoyScore": 82 }, 

      "Consideration": { "CPL": 18.75, "ConvRate": 0.21, "JoyScore": 85 }, 

      "Purchase": { "AOV": 75.2, "ConvRate": 0.32, "JoyScore": 88 }, 

      "Service": { "CSAT": 88, "NPS": 60, "JoyScore": 80 }, 

      "Loyalty": { "Retention": 0.72, "CLTV": 980, "JoyScore": 85 } 

    }, 

    "Product B": { 

      ... 

    } 

  }, 

  "selm": { 

    "Product A": { "LeadGen": 3, "Qualification": 4, "Closing": 2 } 

  }, 

  "silm": { 

    "Product A": { "SupportTime": 12, "FCR": 0.7, "RetentionPrograms": "Basic" } 

  }, 

  "comments": { 

    "Product A": { 

      "Service": [ 

        "Support team was great!", 

        "Had to wait 3 days for a response." 

      ] 

    } 

  }, 

  "actions": [ 

    { "unit":"Product A", "stage":"Purchase", "recommendation":"Shorten sales cycle by 2 days", "npv":35, "complexity":"Medium", "quarter":"Q3" } 

  ] 

} 

 

############################################ 

## 5 ▸ UX DETAILS 

############################################ 

- Top nav tabs for the 5 major views 

- Inside Journey tab: nested sub-tabs or stage switcher 

- Left sidebar: product unit dropdown (filters all tabs) 

- Right side: <JoycePanel /> collapsible assistant 

- Consistent chart styling (peer lines, confidence bands, trendlines) 

- All widgets include tooltip with: “Data source: internal CRM or benchmark” 

 

############################################ 

## 6 ▸ DELIVERABLES 

############################################ 

1. Vite-based local dev app (`npm install && npm run dev`) 

2. Route: `/report/product-service` 

3. All tabs fully rendered with placeholder mock data 

4. Responsive layout w/ interactive charts 

5. Joyce Q&A pane scaffolded and tied to tab context 

 

############################################ 

# END PROMPT – Generate full codebase now 

############################################ 

 