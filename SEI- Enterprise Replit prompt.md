Below is a copy-paste-ready prompt you can drop straight into Replit AI / Replit Ghostwriter (or any other code-gen assistant inside Replit). 
It tells the tool exactly what to scaffold: tech stack, file tree, components, placeholder data, and interactions for the Enterprise Layer workspace you just designed. 

python 

CopyEdit 

# PROJECT: Rejoyce.AI – SEI Report ▸ Digital Mirror ▸ Enterprise Layer 

# GOAL: Generate a runnable web-app prototype (React + Vite + Tailwind) that 

# renders the Enterprise Layer as a multi-tab dashboard with interactive charts 

# and a built-in “Joyce Agent” chat panel. 

 

############################################ 

## 1 ▸ TECH STACK & LIBRARIES 

############################################ 

- React 18 with Vite starter 

- TailwindCSS for styling (already configured) 

- Recharts for all charts (line, bar, radar) 

- Lucide-react icons 

- Zustand or React Context for global state 

- Simple Express-style mock API (or local JSON) for data 

- Optional: react-router-dom for page routing 

 

############################################ 

## 2 ▸ FILE / FOLDER MAP 

############################################ 

src/ 

 ┣ App.jsx                    ⟶ high-level layout + routes 

 ┣ components/ 

 ┃   ┣ NavTabs.jsx            ⟶ horizontal tab bar 

 ┃   ┣ SnapshotGrid.jsx       ⟶ traffic-light KPI grid 

 ┃   ┣ TrendChart.jsx         ⟶ generic line / bar chart wrapper 

 ┃   ┣ RadarMaturity.jsx      ⟶ Joy Score radar chart 

 ┃   ┣ ValueBridge.jsx        ⟶ bar-bridge EV / ROIC visual 

 ┃   ┣ RiskRadar.jsx          ⟶ list with colored severity dots 

 ┃   ┣ ActionCard.jsx         ⟶ NPV / complexity card component 

 ┃   ┗ JoycePanel.jsx         ⟶ collapsible chat sidebar (dummy UI) 

 ┣ pages/ 

 ┃   ┗ EnterpriseLayer.jsx    ⟶ hosts six sub-tabs listed below 

 ┣ data/ 

 ┃   ┗ enterpriseMock.json    ⟶ placeholder KPI + maturity data 

 ┗ hooks/, store/, index.css, main.jsx … 

 

############################################ 

## 3 ▸ ENTERPRISE LAYER – TAB SPECS 

############################################ 

Tab order & component mapping:   

1. **Snapshot**   

   - <SnapshotGrid />   

   - Top-5-Findings list (click → open JoycePanel pre-filled with Q&A)   

   - Peer positioning mini-chart (use <TrendChart /> with quartile bands)   

 

2. **KPI Explorer**   

   - Accordion for Growth, Profit, Return, Efficiency, Liquidity, Cash, TSR   

   - Inside each accordion: <TrendChart /> + FogScore bullet + “Ask Joyce” button   

 

3. **Industry Pack**   

   - Detect industry from mock data (“SaaS” by default)   

   - Render industry-specific KPI cards & peer box-plots (<TrendChart type="box" />)   

 

4. **Maturity Impact**   

   - <RadarMaturity /> (6-point Joy Score)   

   - Correlation matrix table (heat-map colored cells)   

 

5. **Strategic Implications**   

   - <ValueBridge /> (EV bridge)   

   - Scenario toggles (Base | Peer Median | Optimized) update charts live   

   - <RiskRadar /> red-flag list   

 

6. **Recommended Actions**   

   - Grid of <ActionCard /> components sorted by NPV desc   

   - Each card shows lever, benefit $, complexity tag, target owner & quarter   

 

############################################ 

## 4 ▸ UX DETAILS 

############################################ 

- Persistent breadcrumb: “SEI Report ▸ Digital Mirror ▸ Enterprise Layer”   

- JoycePanel slides in from right (use 30 % width). Stub with static answers.   

- Color logic: green ≥ peer P75, yellow = P25-P75, red < P25.   

- Charts need toggle buttons for YoY / QoQ / vs. peer.   

- Include helpful skeleton loaders for async data fetch.   

 

############################################ 

## 5 ▸ PLACEHOLDER DATA SCHEMA (enterpriseMock.json) 

############################################ 

{ 

  "meta": { "company": "Acme Co", "industry": "SaaS", "period": "FY22-FY24" }, 

  "kpis": { 

    "revenueCAGR": { "values": [0.15,0.18,0.17], "peerP25":0.10, "peerP75":0.20, "fog":0.05 }, 

    "ebitdaMargin": { … }, 

    ... 

  }, 

  "joyScore": { "strategy":2, "operations":3, "tech":2, "people":2, "customer":3, "financial":2 }, 

  "selm": { "define":2, "decompose":3, "execute":2, "validate":2 }, 

  "topFindings": [ 

    "ROIC 220 bps below peer median due to asset drag", 

    "EBITDA margin dropped 180 bps in Q4-24 after M&A integration" 

  ], 

  "actions": [ 

    { "name":"Reduce OpEx % by 3 pts", "npv":75, "complexity":"Medium", "owner":"COO", "quarter":"Q3" }, 

    ... 

  ], 

  "risks": [ 

    { "risk":"Debt/EBITDA > 4.0×", "severity":"High" }, 

    ... 

  ] 

} 

 

############################################ 

## 6 ▸ NICE-TO-HAVE EXTENSIONS (OPTIONAL) 

############################################ 

- Integrate OpenAI API for real Joyce responses   

- Dark-mode toggle   

- Export dashboard section to PDF   

- Unit tests with vitest + react-testing-library   

 

############################################ 

## 7 ▸ DELIVERABLES 

############################################ 

1. Working local dev server (`npm install && npm run dev`)   

2. EnterpriseLayer page reachable at `/report/enterprise`   

3. Clean, reusable components adhering to file map   

4. README with setup + mock-data editing instructions   

 

############################################ 

# END PROMPT – Generate full codebase now 

############################################ 

How to use 

Create a new Replit project (React + Vite). 

Paste the above prompt into Ghostwriter or the “AI Generate” box. 

Let it scaffold; then npm install and npm run dev. 

Tweak the mock JSON or wire it to real data later. 

This gives you a fully navigable Enterprise Layer prototype you can iterate, demo to investors, or hand to engineering for production hardening. Enjoy! 

 