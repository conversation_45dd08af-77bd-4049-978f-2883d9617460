Here's your Replit-ready prompt to generate a Joyce Agent prototype aligned with your SEI system design. This version builds off your foundational system (SEI report, <PERSON>, <PERSON> across layers) and incorporates the most recent deep research themes: persistent memory, Slack-style collaboration, AI project manager behaviors, contextual SEI integration, and explainability. 

Shape 

python 

CopyEdit 

# PROJECT: Rejoyce.AI ▸ Joyce Agent Prototype 

# GOAL: Build a working prototype of the Joyce Agent that can: 

# (1) Respond to SEI layer data (KPI, causal graph, Mirror, etc.), 

# (2) <PERSON><PERSON> as a trusted AI consultant, project manager, and coach, 

# (3) Persist and organize conversations across users, 

# (4) Allow @-mentions and shared threads like <PERSON>lack, 

# (5) Operate with contextual awareness per SEI report section. 

 

############################################ 

## 1 ▸ TECH STACK 

############################################ 

- React 18 + Vite 

- TailwindCSS for UI 

- Zustand or Redux for state 

- Socket.io or simple WebSocket backend for real-time messaging 

- Markdown renderer for chat messages 

- Lucide-react for icons 

- Simple Node/Express mock API or Firebase for persistence 

- Optional: OpenAI API (for Joyce responses), Pinecone or Chroma for RAG simulation 

 

############################################ 

## 2 ▸ KEY FEATURES TO IMPLEMENT 

############################################ 

 

🧠 **Joyce Core Interface** 

- Sidebar assistant accessible from anywhere in the SEI system 

- Main chat panel with streaming response 

- Message composer with suggested prompts (e.g., "Explain this KPI gap", "Draft QBR priorities") 

 

🗃 **Conversation Persistence** 

- Save each Joyce thread by user, layer (Enterprise/Product/Customer), and KPI or object ID 

- Support filtering and resuming prior threads 

- Tag threads with strategic focus: Cost Takeout, Growth, Innovation, etc. 

 

👥 **Multi-user Collaboration** 

- Ability to add other users into a Joyce thread 

- "@Joyce" to re-engage the agent within a collaborative thread 

- Shared reply view like Slack thread or Notion AI comments 

 

🧑‍🏫 **Joyce Persona System** 

- Joyce responds with tones from these presets: 

  - 🎯 Consultant (analytical, ROI-driven) 

  - 🤝 Coach (empathetic, supportive) 

  - 📋 Project Manager (organized, actionable) 

- Responses are based on prompt injection of maturity score, Fog Score, and SEI layer context 

- Joyce clearly discloses data source and confidence 

 

📊 **Contextual Memory** 

- Knows which SEI layer or object the user is interacting with (e.g., Product KPI view) 

- Auto-injects metadata into the query behind the scenes (e.g., “Enterprise Layer ▸ Gross Margin ▸ Fog Score: 3/5”) 

 

🔒 **Trust and Transparency** 

- Every answer includes: 

  - 📌 Source data (SEI object or system field) 

  - 📉 Confidence/Fog level 

  - 📖 Option to “Show reasoning” 

- “Not enough data” fallback with suggested next steps or questions to ask a human 

 

############################################ 

## 3 ▸ FILE STRUCTURE 

############################################ 

 

src/ 

 ┣ App.jsx 

 ┣ data/ 

 ┃   ┗ seiMock.json               ⟶ Enterprise, Product, Customer layer mock data 

 ┣ pages/ 

 ┃   ┗ ReportPage.jsx             ⟶ Mount JoyceAgent + Mirror/SEI layout 

 ┣ components/ 

 ┃   ┣ JoyceAgent.jsx             ⟶ Main assistant window, streaming UI 

 ┃   ┣ JoyceThreadSidebar.jsx     ⟶ List of saved chats 

 ┃   ┣ JoyceMessage.jsx           ⟶ Markdown message bubble + attribution 

 ┃   ┣ JoyceComposer.jsx          ⟶ Prompt composer + tone toggle 

 ┃   ┣ JoycePromptTemplates.jsx   ⟶ Suggestions by context 

 ┃   ┣ JoycePersonaIcon.jsx       ⟶ Consultant/Coach/PM badge renderer 

 ┃   ┣ JoyceShareBar.jsx          ⟶ Invite users to thread 

 ┃   ┗ ChatContextProvider.jsx    ⟶ Manages global SEI + user context 

 

############################################ 

## 4 ▸ MOCK DATA SCHEMA (seiMock.json) 

############################################ 

 

{ 

  "enterprise": { 

    "GrossMargin": { 

      "actual": 47.3, "target": 50, "benchmark": 52, "fog": 2, 

      "maturity": "Basic", "joy": 65 

    } 

  }, 

  "product": { … }, 

  "customer": { … }, 

  "userProfiles": { 

    "boss": { "role": "COO", "strategicIntent": "cost takeout", "joyPersona": "Consultant" } 

  }, 

  "threads": [ 

    { 

      "id": "thread-123", 

      "title": "Gross Margin Gap Q2", 

      "participants": ["boss", "analystA"], 

      "messages": [ 

        { 

          "from": "boss", 

          "msg": "Why are we missing margin?", 

          "context": "Enterprise ▸ Gross Margin", 

          "joyceReply": { 

            "persona": "Consultant", 

            "content": "Based on SEI data, your margin gap is driven by product mix shift and inefficient SG&A leverage. Your maturity is Basic, suggesting poor spend governance…" 

          } 

        } 

      ] 

    } 

  ] 

} 

 

############################################ 

## 5 ▸ INTERACTION NOTES 

############################################ 

 

- Joyce is accessible via a floating button or keyboard shortcut 

- Threads persist with unique IDs and tags 

- Each message stores context: SEI object, user, date, persona tone, and confidence 

- Add “🧠 Show reasoning” to each Joyce message to view thought chain or formula 

 

############################################ 

## 6 ▸ FINAL OUTPUT 

############################################ 

 

- Joyce Agent running at `/joyce` 

- Shared + persistent conversation interface 

- Responds to SEI context with mock data 

- Handles multi-user collaboration threads 

- Coach/PM/Consultant tone options 

- Ready for LLM backend swap 

 

############################################ 

# END PROMPT – generate full prototype now 

############################################ 

 

Next Steps: 

Create a new Replit (React + Vite) project. 

Paste this prompt into Replit’s AI or Ghostwriter panel. 

Once it scaffolds the app, you can hook it to a real vector DB, OpenAI API, and internal SEI objects. 

Let me know if you want: 

Prompt injection libraries for tone/persona shaping 

UI figma mockups 

Backend prompt routing logic for Joyce's reasoning 

We can layer in progressive capabilities like execution sprint planning, “turn this insight into a slide,” or alert-triggered threads as next features. 

 